from fastapi import FastAP<PERSON>, HTTPException
from fastapi_mcp import add_mcp_server
from app.services.zoho_service import fetch_webinars_list, fetch_webinar_by_meetingKey


app = FastAPI()

mcp_server = add_mcp_server(app, mount_path="/sse", name="MCP Webinar Server - AMX")

# ---------- Z<PERSON><PERSON>ls ----------


@mcp_server.tool()
async def get_webinars() -> dict:
    """
    Fetch the list of all available webinars.

    Returns:
        dict: List of webinars with metadata like title, date, and presenter.
    """
    try:
        res = await fetch_webinars_list()  # returns WebinarResponse (Pydantic)
        return res.dict(exclude_none=True)  # MCP requires plain dict
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching webinars: {e}")


@mcp_server.tool()
async def get_webinar_by_meetingkey(meetingKey: str) -> dict:
    """
    Fetch detailed information about a specific webinar using its meetingKey.

    Args:
        meeting<PERSON>ey (str): Unique identifier for the webinar.

    Returns:
        dict: Webinar session metadata including title, agenda, presenter info, time, and access links.
    """
    try:
        response = await fetch_webinar_by_meetingKey(meetingKey)
        return response
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error fetching webinar by {meetingKey}: {e}"
        )

# ---------- Health Check ----------


@app.get("/health")
def health():
    return {"status": "ok"}
